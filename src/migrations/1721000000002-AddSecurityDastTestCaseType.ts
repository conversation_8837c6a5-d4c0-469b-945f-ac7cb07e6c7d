import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSecurityDastTestCaseType1721000000002 implements MigrationInterface {
  name = 'AddSecurityDastTestCaseType1721000000002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the test_case_types table exists
    const tableExists = await queryRunner.hasTable('test_case_types');

    if (tableExists) {
      // Define test case types to add
      const testCaseTypes = [
        {
          name: 'security - dast',
          description: 'Dynamic Application Security Testing (DAST) test cases for identifying security vulnerabilities'
        },
        {
          name: 'functional',
          description: 'Test cases that verify the functional requirements and business logic'
        },
        {
          name: 'performance',
          description: 'Test cases that verify system performance, load, and stress testing'
        },
        {
          name: 'usability',
          description: 'Test cases that verify user experience and interface usability'
        },
        {
          name: 'integration',
          description: 'Test cases that verify integration between different system components'
        }
      ];

      for (const testCaseType of testCaseTypes) {
        // Check if type already exists
        const existingType = await queryRunner.query(`
          SELECT id FROM "test_case_types" WHERE name = $1
        `, [testCaseType.name]);

        if (existingType.length === 0) {
          // Insert the test case type
          await queryRunner.query(`
            INSERT INTO "test_case_types" (id, name, description, "createdAt", "updatedAt")
            VALUES (
              gen_random_uuid(),
              $1,
              $2,
              NOW(),
              NOW()
            )
          `, [testCaseType.name, testCaseType.description]);
          console.log(`✅ Added "${testCaseType.name}" test case type`);
        } else {
          console.log(`ℹ️ "${testCaseType.name}" test case type already exists`);
        }
      }
    } else {
      console.log('⚠️ test_case_types table does not exist, skipping migration');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the test case types added by this migration
    const testCaseTypeNames = [
      'security - dast',
      'functional',
      'performance',
      'usability',
      'integration'
    ];

    for (const typeName of testCaseTypeNames) {
      await queryRunner.query(`
        DELETE FROM "test_case_types" WHERE name = $1
      `, [typeName]);
      console.log(`🗑️ Removed "${typeName}" test case type`);
    }
  }
}
