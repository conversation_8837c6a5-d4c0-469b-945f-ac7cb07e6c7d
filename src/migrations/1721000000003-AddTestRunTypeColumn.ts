import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTestRunTypeColumn1721000000003 implements MigrationInterface {
  name = 'AddTestRunTypeColumn1721000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add type column to test_runs table
    await queryRunner.addColumn(
      'test_runs',
      new TableColumn({
        name: 'type',
        type: 'varchar',
        length: '50',
        isNullable: false,
        default: "'general'",
        comment: 'Type of test run: general or security-dast'
      })
    );

    console.log('✅ Added type column to test_runs table');

    // Update existing test runs to have 'general' type
    await queryRunner.query(`
      UPDATE "test_runs" SET "type" = 'general' WHERE "type" IS NULL
    `);

    console.log('✅ Updated existing test runs to have general type');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the type column
    await queryRunner.dropColumn('test_runs', 'type');
    console.log('🗑️ Removed type column from test_runs table');
  }
}
